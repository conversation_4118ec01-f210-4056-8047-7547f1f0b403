\documentclass[8pt]{article}
\usepackage{ctex}
\usepackage{indentfirst}
\usepackage{geometry}
\geometry{a4paper, total={6in, 8in}}
\usepackage{xeCJK}
\usepackage[fleqn]{amsmath}
\usepackage{enumitem}
\usepackage{parskip}
\usepackage{fancyhdr}
\usepackage{graphicx}
\usepackage{float}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{multicol}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{ctex}
\usepackage{indentfirst}
\usepackage{indentfirst}
\usepackage{subfiles}
\usepackage{longtable}
\usepackage{multirow}
\usepackage[fleqn]{amsmath}
\usepackage{parskip}
\usepackage{listings}
\usepackage{fancyhdr}
% \usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\pagestyle{fancy}
\usepackage{titlesec}
\usepackage{hyperref}
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}
\usepackage{graphicx}
\usepackage{float}
\usepackage{multicol}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{xcolor}
\usepackage{amsthm}
\usepackage{titlesec}
\usepackage{hyperref}

\titleformat{\section}
  {\normalfont\Large\bfseries}
  {\thesection}{1em}{}
\renewcommand{\thesection}{\Roman{section}.}

\newcommand{\points}[1]{\textbf{(#1 points)}}

\definecolor{gray}{rgb}{0.5, 0.5, 0.5}

\hypersetup{
    colorlinks,
    citecolor=[rgb]{0.00, 0.45, 0.70},
    linkcolor=[rgb]{0.01, 0.62, 0.45},
    urlcolor=[rgb]{0.80, 0.47, 0.74},
}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,     
    captionpos=b,        
    keepspaces=true,     
    numbers=left,        
    numbersep=5pt,       
    showspaces=false,    showstringspaces=false,
    showtabs=false,      
    tabsize=2
}
\lstset{style=mystyle}
\pagestyle{fancy}

% 设置页眉
\fancyhead[L]{2025年春季}
\fancyhead[C]{高级算法}
\fancyhead[R]{作业二}

% 调整 section 格式
\titleformat{\section}
  {\normalfont\Large\bfseries}
  {\thesection}{1em}{}
\renewcommand{\thesection}{\Roman{section}.}

% 设置页面颜色
\definecolor{gray}{rgb}{0.5, 0.5, 0.5}
\hypersetup{
    colorlinks,
    citecolor=[rgb]{0.00, 0.45, 0.70},
    linkcolor=[rgb]{0.01, 0.62, 0.45},
    urlcolor=[rgb]{0.80, 0.47, 0.74},
}

% 页头信息
\title{\textbf{高级算法（Spring 2025）作业二}}
\author{\textbf{\color{blue} \Large 姓名：周天远 \ \ \ 学号：221900448 \ \ \ \today}}
\date{}

\begin{document}

\maketitle


\section{Simple Tabulation Hashing 扩展}

{\color{gray}
课程中介绍的 tabulation hashing 被称为 simple tabulation hashing。 tabulation hashing 还有不少别的扩展和强化。尝试调查并介绍其中一些，并尝试解释该 tabulation hashing 克服了其他 tabulation hashing 的什么问题，有什么优点和缺点，尝试解释为什么。你也可以调查一个你喜欢的哈希函数。你的分数取决于你的解释的详细程度和准确程度。
}

\textbf{解：} \\


\section{Johnson-Lindenstrauss 变换与 $\ell_\infty$ 降维}

{\color{gray}
考虑使用 Johnson-Lindenstrauss Transformation 对某个单位向量进行降维。如果我们保证该单位向量具有较低的 $\ell_\infty$-norm，即对于单位向量 $\mathbf{x} \in \mathbb{S}^{d-1}$ 保证 $\max_i \mathbf{x}_i$ 较低，我们可以在保证正确性的前提下降到更低的维数吗？可以降低到多少维？你的答案应该包含 $d, \epsilon, \delta, \ell_\infty$ 四个参数。尝试证明你的结论。
}

\textbf{解：} \\
考虑单位向量 $\mathbf{x} \in \mathbb{S}^{d-1}$，满足 $\|\mathbf{x}\|_\infty = \max_i |x_i| \leq M$，其中 $M$ 较小。

设标准高斯 JLT 投影矩阵 $A \in \mathbb{R}^{k \times d}$ 的元素服从独立同分布 $\mathcal{N}(0, 1/k)$，目标是在保证：
\[
(1 - \epsilon) \leq \|A\mathbf{x}\|_2^2 \leq (1 + \epsilon)
\]
以概率至少 $1 - \delta$ 成立的条件下，最小化降维维度 $k$。

\textbf{结论：} 即使 $\|\mathbf{x}\|_\infty$ 很小，也不能降低 $k$。即：
\[
k = \Theta\left(\frac{\log(1/\delta)}{\epsilon^2}\right)
\]
是 \textbf{紧的下界}，且与 $\|\mathbf{x}\|_\infty$ 无关。

\textbf{理由：}
\begin{itemize}
  \item 每一行 $(A\mathbf{x})_i = \sum_{j=1}^d A_{ij} x_j$ 是一组独立高斯变量的线性组合。
  \item 由于 $\|\mathbf{x}\|_2 = 1$，则 $(A\mathbf{x})_i \sim \mathcal{N}(0, 1/k)$，无论 $\mathbf{x}$ 的 $\ell_\infty$ 有多小。
  \item 故 $\|A\mathbf{x}\|_2^2 = \sum_{i=1}^k Y_i^2$，其中 $Y_i \sim \mathcal{N}(0, 1/k)$ 独立同分布，分布与 $\mathbf{x}$ 无关。
\end{itemize}

\textbf{使用集中不等式：}
\[
\Pr\left[ \left| \|A\mathbf{x}\|_2^2 - 1 \right| > \epsilon \right] \leq 2\exp\left(-\frac{k\epsilon^2}{8}\right)
\Rightarrow
k \geq \frac{8}{\epsilon^2} \log\left(\frac{2}{\delta}\right)
\]

\textbf{反例说明：} 即使设 $\|\mathbf{x}\|_\infty = \frac{1}{\sqrt{d}}$（如均匀分布向量），投影后 $\|A\mathbf{x}\|_2^2$ 的分布与 $\mathbf{x} = \mathbf{e}_1$ 相同，无法提高集中性。

\textbf{结论：}
\[
\boxed{k = \Theta\left(\frac{\log(1/\delta)}{\epsilon^2}\right)}
\]
其中常数与 $\ell_\infty$-范数无关。

\textbf{备注：} 若使用稀疏 JLT（如 Count Sketch），低 $\ell_\infty$ 向量可能降低误差，但该方法不满足标准 JLT 的高概率误差界，故不适用于本题的单向量精度保证。

\vspace{8em}

\section{LSH 的集合视角与构造}

{\color{gray}
LSH 函数的设计宗旨是尽量使得相邻的点容易碰撞的同时，相距较远的点不容易碰撞。我们可以反过来想象“被放入某一个桶中的数据点的集合”。这个集合是随机的，在课程中介绍的LSH中，这样的集合是一个随机的Hamming cube。从这个角度考虑的话，不严格地说，我们可以想象，“如果一个数据点被放入了这样一个随机的集合中，当这样的集合是什么形状的时候，相邻的数据点比较容易也在这个集合中，而较远的数据点不容易在这个集合中”。
}

\subsection*{(a) Metric Space on $([0,n]^d, \mathrm{d})$}

{\color{gray}
考虑 metric space $([0,n]^d,\mathrm{d})$，$\mathrm{d}(x,y)=\sqrt{\sum_i (\min\{|x_i-y_i|,n-|x_i-y_i|\})^2}$。（想象在每个维度上都“绕回”，即 $(0,0,0,\dots)$ 和 $(n,n,n,\dots)$ 的距离是 $0$。）对于这样的 metric space 你会如何设计 LSH 函数？尝试解释为什么。
}

\textbf{解：} \\
\subsubsection*{度量定义}

\[
\mathrm{d}(x,y) = \sqrt{\sum_{i=1}^d \left( \min\left\{ |x_i - y_i|,  n - |x_i - y_i| \right\} \right)^2}
\]

该度量在每一维上具有周期性（环形结构），即坐标差在模 \(n\) 意义下计算最小距离。

\subsubsection*{LSH 设计：分治投影哈希}

\textbf{核心思想}：利用环形结构的局部欧几里得特性，将高维环面分解为独立的一维环，每维使用模线性哈希，再通过 AND-OR 结构组合。

\textbf{步骤}:

\begin{enumerate}[label=\arabic*.]
    \item \textbf{单维哈希函数}：

    对第$i$维，定义哈希函数：
    
    \[
    h_i^{(b_i)}(x_i) = \left\lfloor \frac{x_i + b_i \bmod n}{w} \right\rfloor
    \]
    其中 \(b_i \sim \text{Uniform}[0, w)\)，\(w\) 为桶宽。
    
    \item \textbf{高维组合函数}：

    定义整体哈希函数为：
    
    \[
    h_{\mathbf{b}}(x) = \left( h_1^{(b_1)}(x_1),  h_2^{(b_2)}(x_2),  \dots,  h_d^{(b_d)}(x_d) \right)
    \]

    \item \textbf{AND-OR 放大结构}：
    \begin{itemize}
        \item AND: \(k\) 个独立函数，需全部碰撞
        \item OR: \(L\) 个 AND 组，只需任一组碰撞
    \end{itemize}
\end{enumerate}

\subsection*{碰撞概率分析}

\textbf{单维碰撞概率}：记 \(\delta_i = \min\{|x_i - y_i|, n - |x_i - y_i|\}\)，则：
\[
\Pr\left[ h_i^{(b_i)}(x_i) = h_i^{(b_i)}(y_i) \right] = \max\left\{ 0,  1 - \frac{\delta_i}{w} \right\}
\]

\textbf{高维碰撞概率}：

\begin{align*}
p_{\text{AND}} &= \prod_{i=1}^d \max\left\{ 0,  1 - \frac{\delta_i}{w} \right\} \\
p &= 1 - \left(1 - p_{\text{AND}}\right)^L
\end{align*}

\textbf{邻近点（\(\mathrm{d}(x,y) \leq r\)）}：设 \(w = \alpha r\)，则
\[
p_{\text{AND}} \geq \left(1 - \frac{1}{\alpha} \right)^d
\]

\textbf{远距点（\(\mathrm{d}(x,y) > cr\)）}：存在 \(\delta_j > \frac{cr}{\sqrt{d}}\)，则：
\[
p_{\text{AND}} \leq 1 - \frac{c}{\alpha \sqrt{d}}
\]

通过选择合适的 \(k, L\)，可实现 LSH 条件：\(p_1 \gg p_2\)。

\subsubsection*{参数优化}

\begin{itemize}
    \item 桶宽：\(w = \Theta\left(\frac{r}{\sqrt{d}}\right)\)
    \item AND 数：\(k = \Theta(1)\)
    \item OR 数：\(L = \Theta\left( \frac{\log(1/p_2)}{p_{\text{AND}}} \right)\)
    \item 最终性能：\(\rho = \frac{\log p_1}{\log p_2} \approx \frac{1}{c}\)
\end{itemize}

\subsubsection*{(b) Sphere Metric Space $(\mathbb{S}^{d-1}, \mathrm{d})$}

{\color{gray}
再考虑球面空间 $(\mathbb{S}^{d-1},\mathrm{d})$，其中 $\mathrm{d}(x,y)=\arccos\langle x,y\rangle$（即所有点都是单位向量，我们用两个向量的夹角角度表示向量之间的距离）。对于这样的 metric space 你会如何设计 LSH 函数？尝试解释为什么。
}

\textbf{解：} \\
\subsubsection*{度量定义}

\[
\mathrm{d}(x,y) = \arccos(\langle x, y \rangle)
\]

即单位向量间夹角。

\subsubsection*{LSH 设计：超平面分割（Hyperplane LSH）}

\begin{enumerate}[label=\arabic*.]
    \item 采样随机单位向量 \(\mathbf{a} \sim \text{Uniform}(\mathbb{S}^{d-1})\)
    \item 定义哈希函数：
    \[
    h_{\mathbf{a}}(x) = \text{sign}(\langle \mathbf{a}, x \rangle)
    \]
    \item 采用 AND-OR 放大结构与前一致
\end{enumerate}

\subsubsection*{碰撞概率分析}

设 \(x,y \in \mathbb{S}^{d-1}\)，夹角 \(\theta = \mathrm{d}(x,y)\)，则：
\[
\Pr\left[ h_{\mathbf{a}}(x) = h_{\mathbf{a}}(y) \right] = 1 - \frac{\theta}{\pi}
\]

\textbf{邻近点（\(\theta \leq r\)）}：
\[
p_1 = 1 - \frac{r}{\pi}
\]

\textbf{远距点（\(\theta > cr\)）}：
\[
p_2 = 1 - \frac{cr}{\pi}
\]

最终性能：
\[
\rho = \frac{\log p_1}{\log p_2} \approx \frac{1}{c} \quad (\text{当 } r \ll \pi)
\]

\subsubsection*{最优性说明}

\begin{itemize}
    \item 碰撞概率线性：\(1 - \theta / \pi\)
    \item 任意球面 LSH 的下界：\(\rho \geq \frac{\log(1 - r/\pi)}{\log(1 - cr/\pi)} \approx \frac{1}{c}\)
    \item 超平面哈希达到该下界，故为最优
\end{itemize}

\section*{总结}

\begin{center}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{空间类型} & \textbf{LSH 设计} & \textbf{关键参数} & \(\rho\) 值 \\
\hline
环面空间 & 模线性哈希 + AND-OR & \(w = \Theta(r / \sqrt{d})\) & \(\approx 1/c\) \\
球面空间 & 超平面 LSH + AND-OR & 无额外参数 & \(= 1/c\) \\
\hline
\end{tabular}
\end{center}

\textbf{几何启发}：
\begin{itemize}
    \item \textbf{环面空间}：局部欧几里得性 + 周期性，分维处理
    \item \textbf{球面空间}：旋转不变性，允许全局分割
\end{itemize}

\section{OSE 维度下界证明}

{\color{gray}
假设 $n\gg d$（不妨假设 $n > 2^d$）。考虑使用一个 column sparsity 只有 $1$ 的矩阵 $\Pi \in \mathbb{R}^{m\times n}$ 进行 Oblivious Subspace Embedding (OSE)。考虑这样的\textbf{随机}矩阵 $A \in \{0,1\}^{n\times d}$：$A$ 的每一列随机选择一个位置为 $1$，其他位置为 $0$。尝试证明，任意\textbf{确定}的 $\Pi$ 为$A$的列空间进行子空间嵌入都至少需要 $m=\Omega(d^2)$，即它作为 OSE 不可能降维到 $o(d^2)$。提示：尝试观察 $\Pi A e_i, \Pi A(e_i+e_j)$ 的 $\ell_2$-norm，以及你可能需要应用 birthday paradox（随机地把 $d$ 个球丢入 $m$ 个桶中，那么要满足 $m=\Omega(d^2)$ 才能使得有 $\Omega(1)$ 的概率，这 $d$ 个球中不会有两个球被丢进了同一个桶中）。
}

\textbf{解：} \\


\section{点容量最大流建模}

{\color{gray}
点容量问题：现在我们的网络流问题中，每条边的容量是无穷大，但是流经每个节点 $i$ 有容量限制 $c_i$。设计一个求解该模型的最大流的算法，尝试解释其正确性。
}

\textbf{解：} \\


\section{采购优化问题建模}

{\color{gray}
某工厂生产产品需 $k$ 种零件。供应商有 $m$ 家，第 $i\in[m]$ 家提供零件 $j\in[k]$ 的价格为 $a_{ij}$，且最多供应 $c_{ij}$ 个。工厂每天需零件 $j\in[k]$ 的数量为 $d_j$。设计一个算法求解最小化总采购成本，尝试解释其正确性。
}

\textbf{解：} \\


\section{二分图匹配与顶点覆盖对偶性}

{\color{gray}
令 $G = (L, R, E)$ 为一个二分图。写出该图最大匹配的线性规划形式，以及最小顶点覆盖的线性规划形式。证明该图中的最大匹配大小等于最小顶点覆盖的大小。这表明，在二分图中，顶点覆盖问题是可以在多项式时间内求解的。
}

\textbf{解：} \\


\section{0-1 背包问题的线性松弛与近似算法}

{\color{gray}
考虑 0-1 背包问题：有 $n$ 个物品，第 $i$ 个物品的价值和重量分别是 $v_i,w_i$，背包容量 $C$。请写出对应的整数规划，和它的线性规划松弛。设计一个根据松弛线性规划最优解取整以取得优秀整数可行解的方案，并尝试分析你的答案与最优解的近似比。
}

\textbf{解：} \\

\end{document}
