# 高级算法大作业

## 作业要求

认真学习所有ppt里的知识（reference目录），并且理解这些知识的理论，用这些理论完成作业main.tex（hw目录）

你的规划就都放在test-planning目录中

注意：

- main.tex是latex文件，一定遵守latex语法

- 另外每一道题使用的哪个ppt的哪个知识也要标出来

- 一定注意数学证明的严格性！

## 当前题目分析

需要完成的题目：
1. **第I题：Simple Tabulation Hashing 扩展** - 需要Hashing理论
2. **第II题：Johnson-Lindenstrauss 变换与 ℓ∞ 降维** - 需要Dimension理论（已有部分内容）
3. **第III题：LSH 的集合视角与构造** - 需要Hashing和Dimension理论（已有部分内容）
4. **第IV题：OSE 维度下界证明** - 需要Dimension理论
5. **第V题：点容量最大流建模** - 需要Flow & LP理论
6. **第VI题：采购优化问题建模** - 需要Flow & LP理论
7. **第VII题：二分图匹配与顶点覆盖对偶性** - 需要Flow & LP理论
8. **第VIII题：0-1 背包问题的线性松弛与近似算法** - 需要Flow & LP理论

## 第一阶段：理论学习进展

### 需要学习的PDF文件：
- [ ] 高级算法（2025）_Hashing.pdf - 哈希相关理论
- [ ] 高级算法（2025）_Dimension.pdf - 降维理论
- [ ] 高级算法（2025）_Flow_&_LP.pdf - 网络流和线性规划
- [ ] 高级算法（2025）_Sketching.pdf - sketching算法（可能用于理解某些哈希技术）
- [ ] 高级算法（2025）_fingerprint.pdf - 指纹算法（可能用于理解某些哈希技术）

### 学习进展：
正在开始理论学习...